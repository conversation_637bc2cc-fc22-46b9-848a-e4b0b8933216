import { defaultCache } from '@serwist/next/worker'
import {
  Serwist,
  NetworkFirst,
  CacheFirst,
  StaleWhileRevalidate,
} from 'serwist'

const OFFLINE_FALLBACK_PAGE = '/offline.html'

const serwist = new Serwist({
  precacheEntries: self.__SW_MANIFEST || [],
  precacheOptions: {
    cleanupOutdatedCaches: true,
    concurrency: 10,
    fallbackToNetwork: true,
    ignoreURLParametersMatching: [],
  },
  skipWaiting: true,
  clientsClaim: true,
  navigationPreload: true,
  fallbacks: {
    // Make sure the offline page is precached
    document: OFFLINE_FALLBACK_PAGE,
  },
  runtimeCaching: [
    // Add a specific rule for navigation requests
    {
      matcher: ({ request }) => request.mode === 'navigate',
      handler: new NetworkFirst({
        networkTimeoutSeconds: 3,
        cacheName: 'pages-cache',
        plugins: [
          {
            handlerDidError: async () => {
              // Try to return the offline page from the cache
              const cachedResponse = await caches.match(OFFLINE_FALLBACK_PAGE)
              if (cachedResponse) return cachedResponse

              // If offline page is not in cache, try to fetch it
              return fetch(OFFLINE_FALLBACK_PAGE).catch(() => {
                // If all fails, return a simple offline message
                return new Response('You are offline', {
                  status: 503,
                  headers: { 'Content-Type': 'text/html' },
                })
              })
            },
          },
        ],
      }),
    },
    // Specific caching strategy for the downloads page
    {
      matcher: ({ url }) => url.pathname === '/downloads',
      handler: new NetworkFirst({
        networkTimeoutSeconds: 3,
        cacheName: 'downloads-page-cache',
        plugins: [
          {
            handlerDidError: async () => {
              // For the downloads page, try to return it from cache if available
              const cachedResponse = await caches.match('/downloads')
              if (cachedResponse) return cachedResponse

              // If not cached, return the offline page
              const offlineResponse = await caches.match(OFFLINE_FALLBACK_PAGE)
              if (offlineResponse) return offlineResponse

              // Final fallback
              return new Response('Downloads page is not available offline', {
                status: 503,
                headers: { 'Content-Type': 'text/html' },
              })
            },
          },
        ],
      }),
    },
    // Enhanced caching for JavaScript chunks and dynamic imports
    {
      matcher: ({ url }) => url.pathname.includes('/_next/static/chunks/'),
      handler: new CacheFirst({
        cacheName: 'next-js-chunks',
        plugins: [
          {
            cacheWillUpdate: async ({ response }) => {
              return response.status === 200 ? response : null
            },
          },
        ],
      }),
    },
    // Cache UI components and dynamic imports
    {
      matcher: ({ url, request }) =>
        request.destination === 'script' &&
        (url.pathname.includes('/ui/') ||
          url.pathname.includes('/hooks/') ||
          url.pathname.includes('/utils/')),
      handler: new CacheFirst({
        cacheName: 'ui-components',
        plugins: [
          {
            cacheWillUpdate: async ({ response }) => {
              return response.status === 200 ? response : null
            },
          },
        ],
      }),
    },
    // Use StaleWhileRevalidate for CSS and static assets that benefit from freshness
    // but where serving stale content is acceptable
    {
      matcher: ({ url }) =>
        url.pathname.includes('/_next/static/css/') ||
        url.pathname.endsWith('.css'),
      handler: new StaleWhileRevalidate({
        cacheName: 'css-assets',
        plugins: [
          {
            cacheWillUpdate: async ({ response }) => {
              return response.status === 200 ? response : null
            },
          },
        ],
      }),
    },
    ...defaultCache,
  ],
})

serwist.addEventListeners()
