<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Downloads - Offline</title>
    <style>
      body {
        font-family:
          system-ui,
          -apple-system,
          BlinkMacSystemFont,
          'Segoe UI',
          Roboto,
          Oxygen,
          Ubuntu,
          Cantarell,
          'Open Sans',
          'Helvetica Neue',
          sans-serif;
        margin: 0;
        padding: 20px;
        background-color: #f9fafb;
        color: #333;
        line-height: 1.6;
      }
      .container {
        max-width: 800px;
        margin: 0 auto;
        background-color: #fff;
        padding: 30px;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      }
      h1 {
        font-size: 2.5em;
        margin-bottom: 0.5em;
        color: #1f2937;
        text-align: center;
      }
      .offline-notice {
        background-color: #dbeafe;
        border: 1px solid #93c5fd;
        padding: 20px;
        border-radius: 8px;
        margin: 20px 0;
      }
      .offline-notice h2 {
        margin: 0 0 10px 0;
        color: #1e40af;
        font-size: 1.3em;
      }
      .offline-notice p {
        margin: 0;
        color: #1e3a8a;
      }
      .info-section {
        background-color: #f0f9ff;
        border: 1px solid #bae6fd;
        padding: 20px;
        border-radius: 8px;
        margin: 20px 0;
      }
      .info-section h3 {
        margin: 0 0 15px 0;
        color: #0c4a6e;
      }
      .info-section ul {
        margin: 10px 0;
        padding-left: 20px;
      }
      .info-section li {
        margin: 8px 0;
        color: #0c4a6e;
      }
      .button-group {
        display: flex;
        gap: 15px;
        margin: 30px 0;
        flex-wrap: wrap;
        justify-content: center;
      }
      .btn {
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-size: 16px;
        font-weight: 500;
        text-decoration: none;
        display: inline-block;
        text-align: center;
        transition: all 0.2s ease;
      }
      .btn-primary {
        background-color: #3b82f6;
        color: white;
      }
      .btn-primary:hover {
        background-color: #2563eb;
      }
      .btn-secondary {
        background-color: #6b7280;
        color: white;
      }
      .btn-secondary:hover {
        background-color: #4b5563;
      }
      .storage-info {
        background-color: #f3f4f6;
        border: 1px solid #d1d5db;
        padding: 15px;
        border-radius: 6px;
        margin: 20px 0;
        font-size: 14px;
        color: #4b5563;
      }
      .footer {
        text-align: center;
        margin-top: 40px;
        padding-top: 20px;
        border-top: 1px solid #e5e7eb;
        color: #6b7280;
        font-size: 14px;
      }
      @media (max-width: 640px) {
        .container {
          padding: 20px;
          margin: 10px;
        }
        h1 {
          font-size: 2em;
        }
        .button-group {
          flex-direction: column;
        }
        .btn {
          width: 100%;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>Video Downloads</h1>

      <div class="offline-notice">
        <h2>🔌 Offline Mode</h2>
        <p>
          You're currently offline. The full downloads page with video playback
          couldn't load, but your downloaded videos are safely stored on this
          device.
        </p>
      </div>

      <div class="info-section">
        <h3>📱 About Your Downloaded Videos</h3>
        <p>
          Your videos are stored locally using your browser's storage system.
          Here's what you need to know:
        </p>
        <ul>
          <li>
            <strong>Storage Location:</strong> Videos are stored in your
            browser's IndexedDB
          </li>
          <li>
            <strong>Offline Access:</strong> Videos remain available even
            without internet
          </li>
          <li>
            <strong>Device Specific:</strong> Downloads are tied to this browser
            on this device
          </li>
          <li>
            <strong>Automatic Cleanup:</strong> Videos may be removed if storage
            space is needed
          </li>
        </ul>
      </div>

      <div class="storage-info">
        <strong>💾 Storage Information:</strong><br />
        To access your downloaded videos, you'll need to be online so the full
        downloads page can load. The videos themselves don't require internet
        once downloaded.
      </div>

      <div class="button-group">
        <button class="btn btn-primary" onclick="window.location.reload()">
          🔄 Try to Reload
        </button>
        <a href="/" class="btn btn-secondary"> 🏠 Back to Home </a>
      </div>

      <div class="info-section">
        <h3>🔧 Troubleshooting</h3>
        <p>If you continue to have issues accessing your downloads:</p>
        <ul>
          <li>Check your internet connection and try reloading</li>
          <li>Clear your browser cache and cookies</li>
          <li>Make sure JavaScript is enabled in your browser</li>
          <li>Try accessing the page in an incognito/private window</li>
          <li>Update your browser to the latest version</li>
        </ul>
      </div>

      <div class="footer">
        <p>
          This is a simplified offline version of the downloads page.<br />
          Connect to the internet to access full functionality.
        </p>
      </div>
    </div>

    <script>
      // Simple script to detect when back online
      window.addEventListener('online', function () {
        const notice = document.querySelector('.offline-notice')
        if (notice) {
          notice.innerHTML = `
            <h2>✅ Back Online!</h2>
            <p>Your internet connection has been restored. Reloading the page to access full functionality...</p>
          `
          notice.style.backgroundColor = '#d1fae5'
          notice.style.borderColor = '#86efac'
          notice.querySelector('h2').style.color = '#065f46'
          notice.querySelector('p').style.color = '#064e3b'

          // Auto-reload after 2 seconds
          setTimeout(() => {
            window.location.reload()
          }, 2000)
        }
      })

      // Check if we're actually online (sometimes the event doesn't fire)
      if (navigator.onLine) {
        setTimeout(() => {
          fetch('/downloads', { method: 'HEAD' })
            .then(() => {
              window.location.reload()
            })
            .catch(() => {
              // Still offline or server issues
            })
        }, 1000)
      }
    </script>
  </body>
</html>
